import 'dart:convert';
import 'dart:math';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:twitter_login/twitter_login.dart';
import 'package:wordle/src/core/utils/env.dart';
import 'package:crypto/crypto.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AuthService {
  final _auth = FirebaseAuth.instance;

  bool isAuthenticated() {
    return _auth.currentUser != null;
  }

  User? get user => _auth.currentUser;

  bool get isUserAnonymous => _auth.currentUser?.isAnonymous ?? false;

  Future<UserCredential> signInAsGuest() async {
    return await _auth.signInAnonymously();
  }

  Future<UserCredential> signInWithGoogle({bool linkGuest = false}) async {
    // Trigger the authentication flow
    final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

    // Obtain the auth details from the request
    final GoogleSignInAuthentication? googleAuth =
        await googleUser?.authentication;

    // Create a new credential
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth?.accessToken,
      idToken: googleAuth?.idToken,
    );

    if (!linkGuest) {
      // Once signed in, return the UserCredential
      return await _auth.signInWithCredential(credential);
    } else {
      // If already signed as guest, link the account to social
      return await _auth.currentUser!.linkWithCredential(credential);
    }
  }

  Future<UserCredential> signInWithFacebook({bool linkGuest = false}) async {
    // Trigger the sign-in flow
    final LoginResult loginResult = await FacebookAuth.instance.login();

    // Create a credential from the access token
    final OAuthCredential facebookAuthCredential =
        FacebookAuthProvider.credential(loginResult.accessToken?.tokenString ?? '');

    if (!linkGuest) {
      // Once signed in, return the UserCredential
      return await _auth.signInWithCredential(facebookAuthCredential);
    } else {
      // If already signed as guest, link the account to social
      return await _auth.currentUser!
          .linkWithCredential(facebookAuthCredential);
    }
  }

  Future<UserCredential> signInWithTwitter({bool linkGuest = false}) async {
    // Create a TwitterLogin instance
    final twitterLogin = TwitterLogin(
      apiKey: env(EnvKey.TWITTER_APIKEY),
      apiSecretKey: env(EnvKey.TWITTER_APISECRET),
      redirectURI: env(EnvKey.CALLBACK_URI),
    );

    // Trigger the sign-in flow
    final authResult = await twitterLogin.loginV2();

    // Create a credential from the access token
    final twitterAuthCredential = TwitterAuthProvider.credential(
      accessToken: authResult.authToken ?? '',
      secret: authResult.authTokenSecret ?? '',
    );

    if (!linkGuest) {
      // Once signed in, return the UserCredential
      return await _auth.signInWithCredential(twitterAuthCredential);
    } else {
      // If already signed as guest, link the account to social
      return await _auth.currentUser!.linkWithCredential(twitterAuthCredential);
    }
  }

  Future<UserCredential> signInWithApple({bool linkGuest = false}) async {
    // To prevent replay attacks with the credential returned from Apple, we
    // include a nonce in the credential request. When signing in with
    // Firebase, the nonce in the id token returned by Apple, is expected to
    // match the sha256 hash of `rawNonce`.
    final rawNonce = _generateNonce();
    final nonce = _sha256ofString(rawNonce);

    // Request credential for the currently signed in Apple account.
    final appleCredential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
      nonce: nonce,
    );

    // Create an `OAuthCredential` from the credential returned by Apple.
    final oauthCredential = OAuthProvider("apple.com").credential(
      idToken: appleCredential.identityToken,
      rawNonce: rawNonce,
    );

    if (!linkGuest) {
      // Sign in the user with Firebase. If the nonce we generated earlier does
      // not match the nonce in `appleCredential.identityToken`, sign in will fail.
      return await _auth.signInWithCredential(oauthCredential);
    } else {
      // If already signed as guest, link the account to social
      return await _auth.currentUser!.linkWithCredential(oauthCredential);
    }
  }

  Future<void> signOut() async {
    await _auth.signOut();
  }

  Future<void> refreshUser() async {
    if (_auth.currentUser != null) {
      await _auth.currentUser?.reload();
    }
  }

  String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }

  /// Returns the sha256 hash of [input] in hex notation.
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}

extension UserExt on User {
  String? getDisplayName() {
    if (providerData.isNotEmpty) {
      final userInfo = providerData.first;
      if (userInfo.displayName != null && userInfo.displayName!.isNotEmpty) {
        return userInfo.displayName;
      } else {
        return displayName;
      }
    } else {
      return displayName;
    }
  }

  String? getPhotoURL() {
    if (providerData.isNotEmpty) {
      final userInfo = providerData.first;

      if (userInfo.photoURL != null && userInfo.photoURL!.isNotEmpty) {
        return userInfo.photoURL;
      } else {
        return photoURL;
      }
    } else {
      return photoURL;
    }
  }
}
